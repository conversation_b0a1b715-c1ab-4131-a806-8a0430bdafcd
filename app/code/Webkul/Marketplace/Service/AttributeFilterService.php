<?php

declare(strict_types=1);

namespace Webkul\Marketplace\Service;

use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\AuthorizationException;
use Psr\Log\LoggerInterface;

/**
 * Service class to handle EAV attribute filtering for product collections
 */
class AttributeFilterService
{
    private const PRODUCT_ENTITY_TYPE_ID = 4;
    private const DEFAULT_STORE_ID = 0;

    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger
    ) {}

    /**
     * Apply EAV attribute filter to product collection
     *
     * @param Collection $collection
     * @param string $attributeCode
     * @param string $searchValue
     * @return void
     * @throws LocalizedException
     * @throws AuthorizationException
     */
    public function applyAttributeFilter(Collection $collection, string $attributeCode, string $searchValue): void
    {
        if (empty($searchValue)) {
            return;
        }

        try {
            $connection = $this->getConnection();
            $attributeId = $this->getAttributeId($attributeCode, $connection);

            if (!$attributeId) {
                $this->logger->warning(
                    'Attribute not found for filtering',
                    ['attribute_code' => $attributeCode]
                );
                return;
            }

            if (!$this->isAttributeSellerEditable($attributeId, $connection)) {
                throw new AuthorizationException(
                    __('You are not authorized to filter by the "%1" attribute.', $attributeCode)
                );
            }

            $this->applyAttributeJoinAndFilter($collection, $attributeId, $attributeCode, $searchValue, $connection);

        } catch (AuthorizationException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->logger->error(
                'Error applying attribute filter',
                [
                    'attribute_code' => $attributeCode,
                    'search_value' => $searchValue,
                    'error' => $e->getMessage()
                ]
            );
            throw new LocalizedException(__('Unable to apply filter for attribute: %1', $attributeCode));
        }
    }

    /**
     * Get database connection
     *
     * @return AdapterInterface
     */
    private function getConnection(): AdapterInterface
    {
        return $this->resourceConnection->getConnection();
    }

    /**
     * Get attribute ID by attribute code
     *
     * @param string $attributeCode
     * @param AdapterInterface $connection
     * @return int|null
     */
    private function getAttributeId(string $attributeCode, AdapterInterface $connection): ?int
    {
        $eavAttributeTable = $connection->getTableName('eav_attribute');

        $select = $connection->select()
            ->from(['ea' => $eavAttributeTable], ['attribute_id'])
            ->where('ea.entity_type_id = ?', self::PRODUCT_ENTITY_TYPE_ID)
            ->where('ea.attribute_code = ?', $attributeCode);

        $attributeId = $connection->fetchOne($select);

        return $attributeId ? (int)$attributeId : null;
    }

    /**
     * Check if attribute is seller editable
     *
     * @param int $attributeId
     * @param AdapterInterface $connection
     * @return bool
     */
    private function isAttributeSellerEditable(int $attributeId, AdapterInterface $connection): bool
    {
        $catalogEavAttributeTable = $connection->getTableName('catalog_eav_attribute');

        $select = $connection->select()
            ->from(['cea' => $catalogEavAttributeTable], ['is_seller_editable'])
            ->where('cea.attribute_id = ?', $attributeId);

        $isSellerEditable = $connection->fetchOne($select);

        return $isSellerEditable === null || (bool)$isSellerEditable;
    }

    /**
     * Apply join and filter conditions to collection
     *
     * @param Collection $collection
     * @param int $attributeId
     * @param string $attributeCode
     * @param string $searchValue
     * @param AdapterInterface $connection
     * @return void
     */
    private function applyAttributeJoinAndFilter(
        Collection $collection,
        int $attributeId,
        string $attributeCode,
        string $searchValue,
        AdapterInterface $connection
    ): void {
        $attributeTableName = $this->getAttributeTableName($connection, $attributeId);
        $joinCondition = $this->buildJoinCondition($attributeId, $attributeCode, $connection);
        $aliasName = $attributeCode . '_attr';

        $collection->getSelect()->joinLeft(
            [$aliasName => $attributeTableName],
            $joinCondition,
            []
        );

        $collection->getSelect()->where(
            $connection->quoteInto($aliasName . '.value = ?', $searchValue)
        );
    }

    /**
     * Get the appropriate EAV attribute table name
     *
     * @param AdapterInterface $connection
     * @param int $attributeId
     * @return string
     */
    private function getAttributeTableName(AdapterInterface $connection, int $attributeId): string
    {
        return $connection->getTableName('catalog_product_entity_varchar');
    }

    /**
     * Build join condition based on whether the product entity table has row_id
     *
     * @param int $attributeId
     * @param string $attributeCode
     * @param AdapterInterface $connection
     * @return string
     */
    private function buildJoinCondition(int $attributeId, string $attributeCode, AdapterInterface $connection): string
    {
        $productEntityTable = $connection->getTableName('catalog_product_entity');
        $columns = $connection->describeTable($productEntityTable);
        $hasRowId = isset($columns['row_id']);
        $aliasName = $attributeCode . '_attr';

        if ($hasRowId) {
            return sprintf(
                'e.row_id = %s.row_id AND %s.attribute_id = %d AND %s.store_id = %d',
                $aliasName,
                $aliasName,
                $attributeId,
                $aliasName,
                self::DEFAULT_STORE_ID
            );
        }

        return sprintf(
            'e.entity_id = %s.entity_id AND %s.attribute_id = %d AND %s.store_id = %d',
            $aliasName,
            $aliasName,
            $attributeId,
            $aliasName,
            self::DEFAULT_STORE_ID
        );
    }
}
